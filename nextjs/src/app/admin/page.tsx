"use client";
import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { AdminHeader, StatsCard } from '@/components/admin';
import { Users, Store, Package, ShoppingCart, DollarSign } from 'lucide-react';
import { getDashboardStats, getCurrentUserStore } from '@/features/admin/api';
import { DashboardStats } from '@/features/admin/types';
import Link from 'next/link';
import { formatCurrency } from '@/lib/utils/format';
import { StatusBadge } from '@/components/admin';
import { useAuth } from '@/lib/hooks/useAuth';

export default function AdminDashboardPage() {
  const { role } = useAuth();
  const [stats, setStats] = useState<DashboardStats | null>(null);
  const [loading, setLoading] = useState(true);
  const [userStore, setUserStore] = useState<any>(null);

  useEffect(() => {
    const fetchStats = async () => {
      try {
        let storeId: string | undefined;

        // If user is a store owner, get their store first
        if (role === 'store_owner') {
          const { store, error } = await getCurrentUserStore();
          if (error) {
            console.error('Error fetching user store:', error);
          } else if (store) {
            setUserStore(store);
            storeId = store.id;
          }
        }

        // Fetch dashboard stats with store filtering if applicable
        const data = await getDashboardStats(storeId);
        setStats(data);
      } catch (error) {
        console.error('Error fetching dashboard stats:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchStats();
  }, [role]);

  if (loading) {
    return (
      <div className="flex items-center justify-center h-96">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <AdminHeader
        title={role === 'store_owner' ? `${userStore?.name || 'Store'} Dashboard` : "Admin Dashboard"}
        description={role === 'store_owner' ? "Overview of your store" : "Overview of your marketplace"}
      />

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        {role === 'admin' && (
          <>
            <StatsCard
              title="Total Users"
              value={stats?.users_count || 0}
              icon={Users}
              iconColor="text-blue-500"
            />
            <StatsCard
              title="Total Stores"
              value={stats?.stores_count || 0}
              icon={Store}
              iconColor="text-green-500"
            />
          </>
        )}
        <StatsCard
          title={role === 'store_owner' ? "My Products" : "Total Products"}
          value={stats?.products_count || 0}
          icon={Package}
          iconColor="text-amber-500"
        />
        <StatsCard
          title={role === 'store_owner' ? "My Sales" : "Total Sales"}
          value={formatCurrency(stats?.total_sales || 0, 'GMD')}
          icon={DollarSign}
          iconColor="text-purple-500"
        />
        {role === 'store_owner' && (
          <StatsCard
            title="My Orders"
            value={stats?.orders_count || 0}
            icon={ShoppingCart}
            iconColor="text-indigo-500"
          />
        )}
      </div>

      {/* Recent Orders */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">
            {role === 'store_owner' ? 'Recent Orders from My Store' : 'Recent Orders'}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead>
                <tr className="border-b">
                  <th className="text-left py-3 px-2 font-medium">Order ID</th>
                  <th className="text-left py-3 px-2 font-medium">Customer</th>
                  <th className="text-left py-3 px-2 font-medium">Amount</th>
                  <th className="text-left py-3 px-2 font-medium">Status</th>
                  <th className="text-left py-3 px-2 font-medium">Date</th>
                </tr>
              </thead>
              <tbody>
                {stats?.recent_orders && stats.recent_orders.length > 0 ? (
                  stats.recent_orders.map((order) => (
                    <tr key={order.id} className="border-b hover:bg-gray-50">
                      <td className="py-3 px-2">
                        <Link href={`/admin/orders/${order.id}`} className="text-primary-600 hover:underline">
                          {order.id.substring(0, 8)}...
                        </Link>
                      </td>
                      <td className="py-3 px-2">{order.user_email || order.user_id.substring(0, 8)}</td>
                      <td className="py-3 px-2">{formatCurrency(order.total, order.currency)}</td>
                      <td className="py-3 px-2">
                        <StatusBadge status={order.status} />
                      </td>
                      <td className="py-3 px-2">{new Date(order.created_at).toLocaleDateString()}</td>
                    </tr>
                  ))
                ) : (
                  <tr>
                    <td colSpan={5} className="py-4 text-center text-gray-500">
                      No recent orders
                    </td>
                  </tr>
                )}
              </tbody>
            </table>
          </div>
          {stats?.recent_orders && stats.recent_orders.length > 0 && (
            <div className="mt-4 text-right">
              <Link href="/admin/orders" className="text-primary-600 hover:underline text-sm">
                View all orders →
              </Link>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Recent Users - Only show for admins */}
      {role === 'admin' && (
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Recent Users</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead>
                  <tr className="border-b">
                    <th className="text-left py-3 px-2 font-medium">User</th>
                    <th className="text-left py-3 px-2 font-medium">Role</th>
                    <th className="text-left py-3 px-2 font-medium">Joined</th>
                  </tr>
                </thead>
                <tbody>
                  {stats?.recent_users && stats.recent_users.length > 0 ? (
                    stats.recent_users.map((user) => (
                      <tr key={user.id} className="border-b hover:bg-gray-50">
                        <td className="py-3 px-2">
                          <Link href={`/admin/users/${user.id}`} className="text-primary-600 hover:underline">
                            {user.email}
                          </Link>
                        </td>
                        <td className="py-3 px-2">
                          <StatusBadge status={user.role} />
                        </td>
                        <td className="py-3 px-2">{new Date(user.created_at).toLocaleDateString()}</td>
                      </tr>
                    ))
                  ) : (
                    <tr>
                      <td colSpan={3} className="py-4 text-center text-gray-500">
                        No recent users
                      </td>
                    </tr>
                  )}
                </tbody>
              </table>
            </div>
            {stats?.recent_users && stats.recent_users.length > 0 && (
              <div className="mt-4 text-right">
                <Link href="/admin/users" className="text-primary-600 hover:underline text-sm">
                  View all users →
                </Link>
              </div>
            )}
          </CardContent>
        </Card>
      )}
    </div>
  );
}
