import { createSPASassClient } from '@/lib/supabase/client';
import { CartItem, Order } from '@/lib/types/ecommerce';

interface CreateOrderParams {
  userId: string;
  cartItems: CartItem[];
  total: number;
  currency: string;
  shippingAddress?: {
    name?: string;
    email?: string;
    phone?: string;
    address?: string;
    city?: string;
    state?: string;
    country?: string;
    postalCode?: string;
  };
  notes?: string;
}

interface CreatePaymentParams {
  orderId: string;
  amount: number;
  currency: string;
  paymentMethod: string;
  transactionId: string;
  paymentDetails?: Record<string, any>;
}

export class CheckoutService {
  /**
   * Fetch complete product data for cart items
   * Use this to ensure all cart items have complete product data before checkout
   */
  static async enrichCartItems(cartItems: CartItem[]): Promise<CartItem[]> {
    try {
      const client = await createSPASassClient();
      const supabase = client.getSupabaseClient();

      // Get product IDs that need to be fetched
      const productIds = cartItems.map(item => item.product_id);

      // Fetch complete product data including store_id
      const { data: products, error } = await supabase
        .from('products')
        .select('*, store_id')
        .in('id', productIds);

      if (error) throw error;

      // Create a map of products by ID for quick lookup
      const productMap = new Map();
      products.forEach(product => {
        productMap.set(product.id, product);
      });

      // Enrich cart items with complete product data
      return cartItems.map(item => ({
        ...item,
        product: productMap.get(item.product_id) || item.product
      }));
    } catch (error) {
      console.error('Error enriching cart items:', error);
      return cartItems; // Return original items if enrichment fails
    }
  }
  /**
   * Create a new order from cart items
   */
  static async createOrder(params: CreateOrderParams): Promise<{ id: string; status: string }> {
    try {
      // Always enrich cart items first to ensure we have complete product data
      const enrichedCartItems = await this.enrichCartItems(params.cartItems);

      // Check for missing store_ids after enrichment
      const missingStoreProducts = enrichedCartItems.filter(
        item => !item.product || !item.product.store_id
      );

      if (missingStoreProducts.length > 0) {
        const missingIds = missingStoreProducts.map(item => item.product_id).join(', ');
        throw new Error(`Store ID is missing for the following products: ${missingIds}. Please remove these items from your cart and try again.`);
      }

      const client = await createSPASassClient();
      const supabase = client.getSupabaseClient();

      // Create the order with enriched cart items
      const { data: order, error: orderError } = await supabase
        .from('orders')
        .insert({
          user_id: params.userId,
          total: params.total,
          currency: params.currency,
          status: 'pending',
          shipping_name: params.shippingAddress?.name,
          shipping_email: params.shippingAddress?.email,
          shipping_phone: params.shippingAddress?.phone,
          shipping_address: params.shippingAddress?.address,
          shipping_city: params.shippingAddress?.city,
          shipping_state: params.shippingAddress?.state,
          shipping_country: params.shippingAddress?.country,
          shipping_postal_code: params.shippingAddress?.postalCode,
          notes: params.notes,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        })
        .select('id')
        .single();

      if (orderError) throw orderError;

      // Create order items using the enriched cart items
      const orderItems = enrichedCartItems.map(item => ({
        order_id: order.id,
        product_id: item.product_id,
        store_id: item.product.store_id,
        quantity: item.quantity,
        price: item.product?.price || 0,
        total: (item.product?.price || 0) * item.quantity,
        currency: params.currency,
        created_at: new Date().toISOString()
      }));

      const { error: itemsError } = await supabase
        .from('order_items')
        .insert(orderItems);

      if (itemsError) throw itemsError;

      // Create order_store_items manually (instead of relying on trigger)
      // Group order items by store and calculate totals
      const storeGroups = enrichedCartItems.reduce((groups, item) => {
        const storeId = item.product.store_id;
        if (!groups[storeId]) {
          groups[storeId] = {
            store_id: storeId,
            total_amount: 0,
            currency: params.currency
          };
        }
        groups[storeId].total_amount += (item.product?.price || 0) * item.quantity;
        return groups;
      }, {} as Record<string, { store_id: string; total_amount: number; currency: string }>);

      // Insert order_store_items for each store
      const orderStoreItems = Object.values(storeGroups).map(group => ({
        order_id: order.id,
        store_id: group.store_id,
        total_amount: group.total_amount,
        currency: group.currency,
        status: 'pending',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      }));

      if (orderStoreItems.length > 0) {
        const { error: storeItemsError } = await supabase
          .from('order_store_items')
          .insert(orderStoreItems);

        if (storeItemsError) {
          console.error('Error creating order_store_items:', storeItemsError);
          // Don't throw error here - the order was created successfully
          // This is just for store management purposes
        }
      }

      // Clear the cart
      const { error: clearCartError } = await supabase
        .from('cart')
        .delete()
        .eq('user_id', params.userId);

      if (clearCartError) {
        console.error('Error clearing cart:', clearCartError);
        // Continue even if cart clearing fails
      }

      return { id: order.id, status: 'pending' };
    } catch (error) {
      console.error('Error creating order:', error);
      throw new Error(error.message || 'Failed to create order');
    }
  }

  /**
   * Create a payment record for an order
   */
  static async createPayment(params: CreatePaymentParams): Promise<{ id: string; status: string }> {
    try {
      const client = await createSPASassClient();
      const supabase = client.getSupabaseClient();

      const { data: payment, error } = await supabase
        .from('payments')
        .insert({
          order_id: params.orderId,
          amount: params.amount,
          currency: params.currency,
          payment_method: params.paymentMethod,
          payment_status: 'pending',
          transaction_id: params.transactionId,
          payment_details: params.paymentDetails || {},
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        })
        .select('id')
        .single();

      if (error) throw error;

      return { id: payment.id, status: 'pending' };
    } catch (error) {
      console.error('Error creating payment:', error);
      throw new Error('Failed to create payment');
    }
  }

  /**
   * Get order details by ID
   */
  static async getOrder(orderId: string, userId: string): Promise<Order | null> {
    try {
      const client = await createSPASassClient();
      const supabase = client.getSupabaseClient();

      const { data, error } = await supabase
        .from('orders')
        .select(`
          *,
          items:order_items(
            *,
            product:products(*)
          )
        `)
        .eq('id', orderId)
        .eq('user_id', userId)
        .single();

      if (error) throw error;

      return data as unknown as Order;
    } catch (error) {
      console.error('Error getting order:', error);
      return null;
    }
  }

  /**
   * Get payment status for an order
   */
  static async getPaymentStatus(orderId: string): Promise<string> {
    try {
      const client = await createSPASassClient();
      const supabase = client.getSupabaseClient();

      const { data, error } = await supabase
        .from('payments')
        .select('payment_status')
        .eq('order_id', orderId)
        .single();

      if (error) throw error;

      return data.payment_status;
    } catch (error) {
      console.error('Error getting payment status:', error);
      return 'unknown';
    }
  }
}